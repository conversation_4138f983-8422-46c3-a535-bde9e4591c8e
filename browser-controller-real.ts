import { CDP } from "./simple-cdp";

declare global {
  interface Window {
    browserController: any;
  }
}

(function () {
  const config = {
    debug: true,
  };

  let cdpClient: CDP | null = null;
  let sessionId: string | undefined = undefined;
  let isMouseDown = false;

  function log(...args: any[]) {
    if (config.debug) {
      console.log("[browserController]", ...args);
    }
  }

  function error(...args: any[]) {
    console.error("[browserController]", ...args);
  }

  /**
   * Initializes the browser controller with its own CDP session
   * Similar to puppeteer.connect(wsEndpoint) - creates own session context
   * @param browserFullWsEndpoint - WebSocket endpoint for browser connection
   * @param targetId - Target ID to attach to (passed from connections-workflow)
   */
  async function init(
    browserFullWsEndpoint: string,
    targetId: string
  ): Promise<void> {
    log("Connecting to CDP and attaching to target:", targetId);
    await connectToCDPAndAttachToTarget(browserFullWsEndpoint, targetId);
    log("Browser controller initialized with sessionId:", sessionId);
  }

  /**
   * Establishes connection to CDP and attaches to existing target
   * Mimics puppeteer.connect() behavior by connecting to the existing page target
   * @param wsEndpoint - WebSocket endpoint
   */
  async function connectToCDPAndAttachToTarget(
    wsEndpoint: string,
    targetId: string
  ): Promise<void> {
    try {
      // Create CDP connection
      cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });

      log("Attaching to target:", targetId, wsEndpoint);

      // // Attach to the existing page target
      const { sessionId } = await cdpClient.Target.attachToTarget({
        targetId: targetId,
        flatten: true,
      });

      await cdpClient.Page.enable(undefined, sessionId);
      await cdpClient.Runtime.enable(undefined, sessionId);

      // await cdpClient.Target.detachFromTarget({ sessionId: sessionId });
      // log("Detached from target:", targetId, "with sessionId:", sessionId);

      log(
        "✓ Browser controller attached to target",
        targetId,
        "with sessionId:",
        sessionId
      );
    } catch (err) {
      error("Failed to connect to CDP and attach to target:", err);
      throw err;
    }
  }

  // Expose public API
  (globalThis as any).browserController = {
    init,
  };
})();
