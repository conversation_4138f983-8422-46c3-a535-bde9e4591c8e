import puppeteer from "puppeteer";
import { Hyperbrowser } from "@hyperbrowser/sdk";
import fs from "fs";
import { Browserbase } from "@browserbasehq/sdk";

const BROWSERBASE_API_KEY = "bb_live_QwIt78tzGs_zl31pDrEpnMCZ-lI";
const BROWSERBASE_PROJECT_ID = "8965aab4-7075-4993-9f3b-244157f0bd92";

(async () => {
  let browser;
  let wsEndpoint;
  const browserType = "hyperbrowser";
  const useLocalBrowser = false;
  const launchNew = true;

  if (useLocalBrowser) {
    if (launchNew) {
      browser = await puppeteer.launch({
        headless: false,
        defaultViewport: null,
        args: [
          "--remote-debugging-port=9222",
          "--remote-allow-origins=*",
          "--no-first-run",
          "--auto-accept-this-tab-capture",
          "--no-default-browser-check",
        ],
      });
    } else {
      const res = await fetch("http://localhost:9222/json/version");
      const { webSocketDebuggerUrl } = await res.json();

      browser = await puppeteer.connect({
        browserWSEndpoint: webSocketDebuggerUrl,
      });
    }
  } else {
    if (browserType == "browserbase") {
      const bb = new Browserbase({
        apiKey: BROWSERBASE_API_KEY,
      });
      const session = await bb.sessions.create({
        projectId: BROWSERBASE_PROJECT_ID,
      });
      wsEndpoint = session.connectUrl;
    } else if (browserType == "hyperbrowser") {
      const hyperBrowser = new Hyperbrowser({
        apiKey: "hb_28aac10409666bbccf859a9b8804",
        timeout: 60000,
      });
      const session = await hyperBrowser.sessions.create({
        browserArgs: ["--auto-accept-this-tab-capture"],
        device: ["desktop"],
      });
      wsEndpoint = session.wsEndpoint;
    }

    browser = await puppeteer.connect({
      browserWSEndpoint: wsEndpoint,
    });
  }
  console.log({ wsEndpoint });

  const page = await browser.newPage();
  await page.setBypassCSP(true);

  // Enhanced error monitoring and crash detection
  page.on("error", (error) => {
    console.error("💥 PAGE CRASH:", error.message);
    console.error("💥 PAGE CRASH STACK:", error.stack);
  });

  page.on("pageerror", (error) => {
    console.error("💥 SCRIPT ERROR:", error.message);
    console.error("💥 SCRIPT ERROR STACK:", error.stack);
  });

  // Monitor for target crashes
  page.on("close", () => {
    console.error("💥 PAGE CLOSED UNEXPECTEDLY!");
  });

  // Monitor browser disconnection
  browser.on("disconnected", () => {
    console.error("💥 BROWSER DISCONNECTED!");
  });

  // Log browser console messages
  page.on("console", (msg) => {
    const type = msg.type();
    const text = msg.text();
    console.log(`🖥️ BROWSER [${type.toUpperCase()}]: ${text}`);
  });

  console.log("1️⃣ Navigating to github...");
  await page.goto("https://github.com/password_reset", {
    waitUntil: "domcontentloaded",
  });

  console.log(
    "2️⃣ Setting up navigation-safe browser controller with postMessage..."
  );

  // Step 1: Create helper tab via backend CDP and navigate to GitHub for same-origin communication
  const helperTab = await browser.newPage();
  await helperTab.goto("https://github.com", { waitUntil: "domcontentloaded" });
  console.log("✅ Helper tab created and navigated to GitHub");

  // Step 2: Inject the real browser controller into helper tab
  console.log("💉 Injecting real browser controller into helper tab...");

  // First inject the browser controller script
  const script = fs.readFileSync("./browser-controller.min.js", "utf8");
  await helperTab.evaluate(script);

  // Get helper tab target info for browser controller initialization
  const helperTargetId =
    helperTab.target()._targetId || helperTab.target()._targetInfo?.targetId;

  // Initialize browser controller in helper tab
  await helperTab.evaluate(
    (ws, target) => {
      window.browserController.init(ws, target);
    },
    wsEndpoint,
    helperTargetId
  );

  console.log("✅ Real browser controller injected into helper tab");

  // Step 3: Add message handler for cross-tab communication
  await helperTab.evaluate(() => {
    console.log("🤖 Helper tab message handler loaded");

    // Message handler interface that uses the real browser controller
    const messageHandlers = {
      async click(data) {
        console.log("🖱️ Helper: Executing click at", data.x, data.y);
        try {
          // Use real browser controller for click simulation
          const result = await window.browserController.simulateClick(
            data.x,
            data.y
          );
          return result;
        } catch (error) {
          return { success: false, error: error.message };
        }
      },

      async querySelector(data) {
        console.log("🔍 Helper: Querying selector", data.selector);
        try {
          const element = document.querySelector(data.selector);
          return {
            success: true,
            found: !!element,
            tagName: element?.tagName,
            className: element?.className,
            id: element?.id,
            text: element?.textContent?.slice(0, 100),
            href: element?.href,
          };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },

      async takeScreenshot(data) {
        console.log("📸 Helper: Taking screenshot via real browser controller");
        try {
          const result = await window.browserController.takeScreenshot();
          return result;
        } catch (error) {
          return { success: false, error: error.message };
        }
      },

      async getPageInfo(data) {
        console.log("📋 Helper: Getting page info");
        try {
          const result = await window.browserController.getPageInfo();
          return {
            success: true,
            ...result,
            helperUrl: window.location.href,
            helperTitle: document.title,
            isHelper: true,
          };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },

      async getStatus(data) {
        console.log("📊 Helper: Getting browser controller status");
        try {
          const result = await window.browserController.getStatus();
          return result;
        } catch (error) {
          return { success: false, error: error.message };
        }
      },

      async ping(data) {
        console.log("🏓 Helper: Ping received");
        return { success: true, message: "pong", timestamp: Date.now() };
      },
    };

    // Listen for messages from main window
    window.addEventListener("message", async (event) => {
      const { id, type, data, source } = event.data;

      if (source !== "main-page") return;

      console.log("📨 Helper: Received message", { id, type, data });

      try {
        let result;
        if (messageHandlers[type]) {
          result = await messageHandlers[type](data);
        } else {
          result = { success: false, error: `Unknown message type: ${type}` };
        }

        // Send response back to main window
        if (event.source) {
          event.source.postMessage(
            {
              id,
              type: "response",
              result,
              source: "helper-tab",
            },
            "*"
          );
        }
      } catch (error) {
        console.error("💥 Helper: Error handling message", error);
        if (event.source) {
          event.source.postMessage(
            {
              id,
              type: "response",
              result: { success: false, error: error.message },
              source: "helper-tab",
            },
            "*"
          );
        }
      }
    });

    console.log("✅ Helper tab message handler ready");
  });

  console.log("✅ Helper tab script injected");

  // Step 3: Inject client script into main page
  await page.evaluate(() => {
    console.log("📱 Client script loaded");

    let messageId = 0;
    const pendingMessages = new Map();
    let helperWindow = null;

    // Listen for responses from helper tab
    window.addEventListener("message", (event) => {
      const { id, type, result, source } = event.data;

      if (source !== "helper-tab") return;

      if (type === "response" && pendingMessages.has(id)) {
        const { resolve } = pendingMessages.get(id);
        pendingMessages.delete(id);
        resolve(result);
      }
    });

    // Helper function to send messages and wait for response
    function sendMessage(type, data, timeout = 10000) {
      return new Promise((resolve, reject) => {
        const id = ++messageId;

        // Store pending message
        pendingMessages.set(id, { resolve, reject });

        // Set timeout
        setTimeout(() => {
          if (pendingMessages.has(id)) {
            pendingMessages.delete(id);
            reject(new Error(`Message timeout: ${type}`));
          }
        }, timeout);

        // Send message to helper tab
        const message = {
          id,
          type,
          data,
          source: "main-page",
        };

        if (helperWindow) {
          // Direct window reference
          helperWindow.postMessage(message, "*");
        } else {
          // Broadcast to all windows (same origin)
          window.postMessage(message, "*");
          // Also try parent and opener
          if (window.parent && window.parent !== window) {
            window.parent.postMessage(message, "*");
          }
          if (window.opener) {
            window.opener.postMessage(message, "*");
          }
        }
      });
    }

    // Expose browser controller API that communicates with helper tab
    window.browserController = {
      async init() {
        console.log("🚀 Navigation-safe browser controller initialized");

        // Get reference to helper window (same origin now)
        try {
          // Find the helper tab window
          const allWindows = [window.parent, window.opener];
          for (let i = 0; i < 10; i++) {
            try {
              const testWindow = window.open("", `helper-${i}`);
              if (
                testWindow &&
                testWindow.location.href.includes("github.com") &&
                testWindow !== window
              ) {
                helperWindow = testWindow;
                console.log("✅ Found helper window reference");
                break;
              }
            } catch (e) {
              // Ignore access errors
            }
          }

          if (!helperWindow) {
            console.log(
              "⚠️ Helper window not found, will try postMessage to all windows"
            );
          }
        } catch (error) {
          console.log(
            "⚠️ Could not establish direct helper window reference:",
            error.message
          );
        }

        return { success: true };
      },

      async ping() {
        return await sendMessage("ping", {});
      },

      async click(x, y) {
        return await sendMessage("click", { x, y });
      },

      async querySelector(selector) {
        return await sendMessage("querySelector", { selector });
      },

      async getPageInfo() {
        return await sendMessage("getPageInfo", {});
      },

      async getStatus() {
        return await sendMessage("getStatus", {});
      },

      async takeScreenshot() {
        return await sendMessage("takeScreenshot", {});
      },
    };

    console.log("✅ Client browser controller ready");
  });

  console.log("✅ Client script injected into main page");

  // Step 4: Initialize the browser controller
  await page.evaluate(() => {
    return window.browserController.init();
  });

  console.log("✅ Navigation-safe browser controller initialized");

  // Wait for setup to stabilize
  await new Promise((resolve) => setTimeout(resolve, 2000));

  console.log(
    "3️⃣ Testing GitHub logo click with navigation-safe controller..."
  );

  try {
    // Wait for the GitHub logo to be available
    await page.waitForSelector('a[aria-label="Homepage"]', { timeout: 10000 });
    console.log("✅ GitHub logo found");

    console.log("4️⃣ Clicking GitHub logo in 3 seconds...");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Click the GitHub logo using standard Puppeteer (no CDP from page)
    await page.click('a[aria-label="Homepage"]');
    console.log("✅ GitHub logo clicked successfully");

    // Wait to see if browser crashes
    await new Promise((resolve) => setTimeout(resolve, 5000));
    console.log("✅ Browser still alive after 5 seconds");

    // Test browser controller functionality
    console.log("5️⃣ Testing browser controller functionality...");

    // Test ping
    const pingResult = await page.evaluate(() => {
      return window.browserController.ping();
    });
    console.log("🏓 Ping result:", pingResult);

    // Test screenshot via helper tab
    console.log("📸 Testing screenshot via helper tab...");
    const screenshotResult = await page.evaluate(() => {
      return window.browserController.takeScreenshot();
    });
    console.log("📸 Helper tab screenshot result:", screenshotResult);

    // Test page info
    const pageInfoResult = await page.evaluate(() => {
      return window.browserController.getPageInfo();
    });
    console.log("📋 Page info result:", pageInfoResult);

    // Test backend screenshot capability
    console.log("6️⃣ Testing backend screenshot capability...");
    const backendScreenshot = await page.screenshot({ encoding: "base64" });
    console.log(
      "📸 Screenshot taken via backend Puppeteer:",
      backendScreenshot.length,
      "bytes"
    );

    // Test helper tab screenshot capability
    console.log("7️⃣ Testing helper tab screenshot capability...");
    const helperScreenshot = await helperTab.screenshot({ encoding: "base64" });
    console.log(
      "📸 Screenshot taken via helper tab:",
      helperScreenshot.length,
      "bytes"
    );
  } catch (error) {
    console.error("💥 ERROR during GitHub logo test:", error.message);
    console.error("💥 ERROR STACK:", error.stack);
  }

  console.log(
    "🏁 Test completed with navigation-safe architecture - NO CDP FROM PAGE!"
  );
})();
