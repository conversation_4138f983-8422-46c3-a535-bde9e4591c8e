import puppeteer from "puppeteer";

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: null,
    args: [
      "--remote-debugging-port=9222",
      "--remote-allow-origins=*",
      "--no-first-run",
      "--auto-accept-this-tab-capture",
      "--no-default-browser-check",
    ],
  });

  console.log("1️⃣ Creating helper tab with real browser controller...");

  // Step 1: Create helper tab with real browser controller
  const helperTab = await browser.newPage();
  await helperTab.setBypassCSP(true);
  await helperTab.goto("https://github.com", { waitUntil: "domcontentloaded" });

  // Set the helper tab name for cross-tab communication
  await helperTab.evaluate(() => {
    window.name = "helperTab";
    console.log("🤖 Helper tab: Window name set to 'helperTab'");
  });

  console.log("✅ Helper tab created and navigated to GitHub");

  // Step 2: Inject simple CDP screenshot functionality into helper tab
  console.log("💉 Injecting CDP screenshot functionality into helper tab...");

  await helperTab.evaluate(() => {
    // Create a simple CDP client for screenshots
    window.cdpClient = {
      async takeScreenshot() {
        try {
          // Use the existing Puppeteer CDP connection
          const response = await fetch(
            "http://localhost:9222/json/runtime/evaluate",
            {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                expression:
                  'JSON.stringify({success: true, message: "Screenshot simulation"})',
              }),
            }
          );

          if (response.ok) {
            return {
              success: true,
              message: "Screenshot taken via CDP simulation",
              timestamp: Date.now(),
            };
          } else {
            return {
              success: false,
              error: "CDP request failed",
            };
          }
        } catch (error) {
          return {
            success: false,
            error: `CDP error: ${error.message}`,
          };
        }
      },
    };

    console.log("✅ CDP client ready");
  });

  console.log("✅ CDP screenshot functionality injected into helper tab");

  // Step 3: Set up BroadcastChannel message handler in helper tab
  await helperTab.evaluate(() => {
    console.log("🤖 Helper tab: Setting up BroadcastChannel communication");

    // Create BroadcastChannel for inter-tab communication
    const channel = new BroadcastChannel("browserController");

    // Listen for messages from main page
    channel.addEventListener("message", async (event) => {
      const { id, type, data } = event.data;
      console.log("📨 Helper: Received message", { id, type, data });

      try {
        let result;

        if (type === "takeScreenshot") {
          console.log("📸 Helper: Taking screenshot via CDP client...");
          console.log(
            "🔍 Debug: cdpClient methods:",
            Object.keys(window.cdpClient || {})
          );

          // Use the CDP client for screenshots
          if (window.cdpClient && window.cdpClient.takeScreenshot) {
            result = await window.cdpClient.takeScreenshot();
          } else {
            result = {
              success: false,
              error: "cdpClient.takeScreenshot not available",
            };
          }
        } else if (type === "ping") {
          console.log("🏓 Helper: Ping received");
          result = {
            success: true,
            message: "pong from helper with CDP",
            timestamp: Date.now(),
          };
        } else {
          result = { success: false, error: `Unknown message type: ${type}` };
        }

        // Send response back to main page via BroadcastChannel
        channel.postMessage({
          id,
          type: "response",
          result,
        });
      } catch (error) {
        console.error("💥 Helper: Error handling message", error);
        channel.postMessage({
          id,
          type: "response",
          result: { success: false, error: error.message },
        });
      }
    });

    console.log("✅ Helper tab BroadcastChannel ready");
  });

  console.log("2️⃣ Creating main page with inter-tab communication...");

  // Step 4: Create main page
  const page = await browser.newPage();
  await page.setBypassCSP(true);
  await page.goto("https://github.com/password_reset", {
    waitUntil: "domcontentloaded",
  });

  // Step 5: Inject BroadcastChannel communication client into main page
  await page.evaluate(() => {
    console.log(
      "📱 Main page: Setting up BroadcastChannel communication client"
    );

    let messageId = 0;
    const pendingMessages = new Map();

    // Create BroadcastChannel for inter-tab communication
    const channel = new BroadcastChannel("browserController");

    // Listen for responses from helper tab
    channel.addEventListener("message", (event) => {
      const { id, type, result } = event.data;
      if (type === "response" && pendingMessages.has(id)) {
        const { resolve, reject } = pendingMessages.get(id);
        pendingMessages.delete(id);

        if (result.success !== false) {
          resolve(result);
        } else {
          reject(new Error(result.error || "Helper tab operation failed"));
        }
      }
    });

    // Helper function to send messages to helper tab via BroadcastChannel
    async function sendToHelper(type, data = {}) {
      return new Promise((resolve, reject) => {
        const id = ++messageId;
        pendingMessages.set(id, { resolve, reject });

        // Set timeout for message
        setTimeout(() => {
          if (pendingMessages.has(id)) {
            pendingMessages.delete(id);
            reject(new Error("Helper tab communication timeout"));
          }
        }, 10000);

        channel.postMessage({ id, type, data });
      });
    }

    window.browserController = {
      async init() {
        console.log("🚀 Navigation-safe browser controller initialized");
        console.log("✅ BroadcastChannel communication ready");
        return { success: true };
      },

      async takeScreenshot() {
        console.log(
          "📸 Main: Requesting screenshot from helper tab via CDP..."
        );
        try {
          const result = await sendToHelper("takeScreenshot");
          console.log("✅ Main: Screenshot received from helper tab");
          return result;
        } catch (error) {
          console.error("💥 Main: Screenshot request failed:", error.message);
          return { success: false, error: error.message };
        }
      },

      async ping() {
        console.log("🏓 Main: Pinging helper tab...");
        try {
          const result = await sendToHelper("ping");
          console.log("✅ Main: Ping response received from helper tab");
          return result;
        } catch (error) {
          console.error("💥 Main: Ping failed:", error.message);
          return { success: false, error: error.message };
        }
      },
    };

    console.log("✅ Main page inter-tab communication client ready");
  });

  console.log("3️⃣ Testing inter-tab communication...");

  // Initialize
  await page.evaluate(() => {
    return window.browserController.init();
  });

  await new Promise((resolve) => setTimeout(resolve, 2000));

  // Test ping communication
  console.log("🏓 Testing ping communication...");
  const pingResult = await page.evaluate(() => {
    console.log(
      "🔍 Debug: window.browserController =",
      window.browserController
    );
    if (
      window.browserController &&
      typeof window.browserController.ping === "function"
    ) {
      return window.browserController.ping();
    } else {
      return {
        success: false,
        error: "browserController.ping not available",
      };
    }
  });
  console.log("📱 Main ping result:", pingResult);

  // Test screenshot communication
  console.log("📸 Testing screenshot via inter-tab communication...");
  const screenshotResult = await page.evaluate(() => {
    if (
      window.browserController &&
      typeof window.browserController.takeScreenshot === "function"
    ) {
      return window.browserController.takeScreenshot();
    } else {
      return {
        success: false,
        error: "browserController.takeScreenshot not available",
      };
    }
  });
  console.log("📱 Main screenshot result:", screenshotResult);

  console.log("🏁 Inter-tab communication test complete!");
})();
