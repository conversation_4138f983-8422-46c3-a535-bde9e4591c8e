/**
 * Dual CDP GitHub Test Script
 * 
 * This script demonstrates running 2 CDP connections simultaneously:
 * 1. External CDP connection (Node.js script using simple-cdp)
 * 2. Internal CDP connection (injected browser script creating its own CDP connection)
 * 
 * The script opens GitHub.com and injects code that creates a second CDP connection
 * within the browser, allowing us to test dual CDP scenarios for bug hunting.
 */

import { createTarget, CDP, cdp } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

const config = {
  debug: true,
  githubUrl: "https://github.com",
  remoteDebuggingPort: 9222,
};

function log(...args) {
  if (config.debug) {
    console.log('[EXTERNAL-CDP]', ...args);
  }
}

function error(...args) {
  console.error('[EXTERNAL-CDP]', ...args);
}

/**
 * Browser-side script that will be injected to create internal CDP connection
 * This creates the second CDP connection from within the browser
 */
const browserCDPScript = `
(function() {
  const config = {
    debug: true,
    remoteDebuggingPort: 9222,
  };

  function log(...args) {
    if (config.debug) {
      console.log('[INTERNAL-CDP]', ...args);
    }
  }

  function error(...args) {
    console.error('[INTERNAL-CDP]', ...args);
  }

  let internalCdpClient = null;
  let internalSessionId = null;

  /**
   * Creates an internal CDP connection from within the browser
   * This mimics the pattern from the browserController code
   */
  async function createInternalCDPConnection() {
    try {
      log('🔗 Creating internal CDP connection from browser...');
      
      // Get the browser WebSocket endpoint
      const response = await fetch('http://localhost:' + config.remoteDebuggingPort + '/json/version');
      const versionInfo = await response.json();
      const wsEndpoint = versionInfo.webSocketDebuggerUrl;
      
      log('📡 Browser WebSocket endpoint:', wsEndpoint);
      
      // Create WebSocket connection to CDP
      const ws = new WebSocket(wsEndpoint);
      
      return new Promise((resolve, reject) => {
        ws.onopen = () => {
          log('✅ Internal CDP WebSocket connected');
          
          // Send Target.getTargets to list available targets
          const getTargetsMessage = {
            id: 1,
            method: 'Target.getTargets',
            params: {}
          };
          
          ws.send(JSON.stringify(getTargetsMessage));
          
          ws.onmessage = (event) => {
            try {
              const message = JSON.parse(event.data);
              log('📨 CDP Message received:', message);
              
              if (message.id === 1 && message.result) {
                const targets = message.result.targetInfos;
                const pageTargets = targets.filter(t => t.type === 'page');
                log('🎯 Available page targets:', pageTargets.length);
                
                if (pageTargets.length > 0) {
                  const currentTarget = pageTargets.find(t => t.url.includes('github.com')) || pageTargets[0];
                  log('🎯 Selected target:', currentTarget.targetId, currentTarget.url);
                  
                  // Attach to target
                  const attachMessage = {
                    id: 2,
                    method: 'Target.attachToTarget',
                    params: {
                      targetId: currentTarget.targetId,
                      flatten: true
                    }
                  };
                  
                  ws.send(JSON.stringify(attachMessage));
                }
              } else if (message.id === 2 && message.result) {
                internalSessionId = message.result.sessionId;
                log('🔗 Internal CDP attached with sessionId:', internalSessionId);
                
                // Enable Runtime domain for the internal session
                const enableRuntimeMessage = {
                  id: 3,
                  method: 'Runtime.enable',
                  params: {},
                  sessionId: internalSessionId
                };
                
                ws.send(JSON.stringify(enableRuntimeMessage));
                
                resolve({ ws, sessionId: internalSessionId });
              } else if (message.id === 3) {
                log('✅ Internal CDP Runtime domain enabled');
                
                // Test the internal connection by evaluating an expression
                const testMessage = {
                  id: 4,
                  method: 'Runtime.evaluate',
                  params: {
                    expression: 'window.location.href + " - Internal CDP Test: " + Math.random()'
                  },
                  sessionId: internalSessionId
                };
                
                ws.send(JSON.stringify(testMessage));
              } else if (message.id === 4 && message.result) {
                log('🧪 Internal CDP test result:', message.result.result.value);
                log('🎉 Internal CDP connection fully operational!');
              }
            } catch (err) {
              error('❌ Error parsing CDP message:', err);
            }
          };
        };
        
        ws.onerror = (err) => {
          error('❌ Internal CDP WebSocket error:', err);
          reject(err);
        };
        
        ws.onclose = () => {
          log('🔌 Internal CDP WebSocket closed');
        };
      });
      
    } catch (err) {
      error('❌ Failed to create internal CDP connection:', err);
      throw err;
    }
  }

  /**
   * Demonstrates internal CDP functionality
   */
  async function demonstrateInternalCDP() {
    try {
      log('🚀 Starting internal CDP demonstration...');
      
      const connection = await createInternalCDPConnection();
      
      // Store connection globally for inspection
      window.internalCdpConnection = connection;
      
      log('✅ Internal CDP demonstration completed successfully');
      log('🔍 Connection stored in window.internalCdpConnection for inspection');
      
      return {
        success: true,
        sessionId: connection.sessionId,
        message: 'Internal CDP connection established successfully'
      };
      
    } catch (err) {
      error('❌ Internal CDP demonstration failed:', err);
      return {
        success: false,
        error: err.message
      };
    }
  }

  // Store functions globally for external access
  window.browserCDPController = {
    createInternalCDPConnection,
    demonstrateInternalCDP,
    log,
    error
  };

  // Auto-start the demonstration
  log('🎬 Browser CDP script loaded, starting demonstration...');
  demonstrateInternalCDP().then(result => {
    log('🏁 Browser CDP demonstration result:', result);
  });

})();
`;

/**
 * Main function that creates external CDP connection and injects internal CDP script
 */
async function dualCDPTest() {
  log('🚀 Starting Dual CDP GitHub Test...');
  
  try {
    // Step 1: Create external CDP connection and navigate to GitHub
    log('1️⃣ Creating external CDP connection and navigating to GitHub...');
    const targetInfo = await createTarget(config.githubUrl);
    log('🎯 Target created:', targetInfo);
    
    const externalCDP = new CDP(targetInfo);
    
    // Enable necessary domains
    await externalCDP.Runtime.enable();
    await externalCDP.Page.enable();
    log('✅ External CDP domains enabled');
    
    // Wait for page to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Verify we're on GitHub
    const pageInfo = await externalCDP.Runtime.evaluate({
      expression: `({
        title: document.title,
        url: window.location.href,
        isGitHub: window.location.hostname.includes('github.com')
      })`
    });
    
    log('📄 Page loaded:', pageInfo.result.value);
    
    if (!pageInfo.result.value.isGitHub) {
      throw new Error('Failed to navigate to GitHub.com');
    }
    
    // Step 2: Inject the internal CDP script
    log('2️⃣ Injecting internal CDP script into the page...');
    
    const injectionResult = await externalCDP.Runtime.evaluate({
      expression: browserCDPScript
    });
    
    if (injectionResult.exceptionDetails) {
      error('❌ Script injection failed:', injectionResult.exceptionDetails);
      throw new Error('Failed to inject internal CDP script');
    }
    
    log('✅ Internal CDP script injected successfully');
    
    // Step 3: Wait for internal CDP to establish connection
    log('3️⃣ Waiting for internal CDP connection to establish...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    return {
      success: true,
      externalCDP,
      targetInfo
    };
    
  } catch (err) {
    error('❌ Dual CDP test failed:', err);
    throw err;
  }
}

export { dualCDPTest };
