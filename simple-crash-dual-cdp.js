import puppeteer from "puppeteer";
import { Hyperbrowser } from "@hyperbrowser/sdk";
import fs from "fs";
import { Browserbase } from "@browserbasehq/sdk";

const BROWSERBASE_API_KEY = "bb_live_QwIt78tzGs_zl31pDrEpnMCZ-lI";
const BROWSERBASE_PROJECT_ID = "8965aab4-7075-4993-9f3b-244157f0bd92";

(async () => {
  let browser;
  let wsEndpoint;
  const browserType = "hyperbrowser";
  const useLocalBrowser = true;
  const launchNew = true;
  if (useLocalBrowser) {
    if (launchNew) {
      browser = await puppeteer.launch({
        headless: false,
        defaultViewport: null,
        args: [
          "--remote-debugging-port=9222",
          "--remote-allow-origins=*",
          "--no-first-run",
          "--auto-accept-this-tab-capture",
          "--no-default-browser-check",
        ],
      });
    } else {
      const res = await fetch("http://localhost:9222/json/version");
      const { webSocketDebuggerUrl } = await res.json();

      browser = await puppeteer.connect({
        browserWSEndpoint: webSocketDebuggerUrl,
      });
    }
  } else {
    if (browserType == "browserbase") {
      const bb = new Browserbase({
        apiKey: BROWSERBASE_API_KEY,
      });
      const session = await bb.sessions.create({
        projectId: BROWSERBASE_PROJECT_ID,
      });
      wsEndpoint = session.connectUrl;
    } else if (browserType == "hyperbrowser") {
      const hyperBrowser = new Hyperbrowser({
        apiKey: "hb_28aac10409666bbccf859a9b8804",
        timeout: 60000,
      });
      const session = await hyperBrowser.sessions.create({
        browserArgs: ["--auto-accept-this-tab-capture"],
        device: ["desktop"],
      });
      wsEndpoint = session.wsEndpoint;
    }

    browser = await puppeteer.connect({
      browserWSEndpoint: wsEndpoint,
    });
  }
  console.log({ wsEndpoint });

  const page = await browser.newPage();
  await page.setBypassCSP(true);
  // Simple error monitoring - just console logs
  page.on("error", (error) => {
    console.error("💥 PAGE CRASH:", error.message);
  });

  page.on("pageerror", (error) => {
    console.error("💥 SCRIPT ERROR:", error.message);
  });

  page.on("close", () => {
    console.log("💥 Page closed");
  });

  browser.on("disconnected", () => {
    console.log("💥 Browser disconnected");
  });

  // Log browser console messages
  page.on("console", (msg) => {
    const type = msg.type();
    const text = msg.text();
    console.log(`🖥️ BROWSER [${type.toUpperCase()}]: ${text}`);
  });

  console.log("1️⃣ Navigating to github...");
  await page.goto("https://github.com/password_reset", {
    waitUntil: "domcontentloaded",
  });

  console.log("2️⃣ Setting up navigation-safe browser controller...");

  // Step 1: Create helper tab via backend CDP
  const helperTab = await browser.newPage();
  console.log("✅ Helper tab created");
  await helperTab.goto("https://github.com", {
    waitUntil: "domcontentloaded",
  });

  // Step 2: Inject message handler script into helper tab
  await helperTab.evaluate(() => {
    // Helper tab script that handles messages and executes actions
    console.log("🤖 Helper tab script loaded");

    // Create BroadcastChannel for communication
    const channel = new BroadcastChannel("browser-controller");

    // Message handler interface
    const messageHandlers = {
      async click(data) {
        console.log("🖱️ Helper: Executing click at", data.x, data.y);
        // Simulate click using DOM events
        const element = document.elementFromPoint(data.x, data.y);
        if (element) {
          element.click();
          return { success: true, element: element.tagName };
        }
        return { success: false, error: "No element found at coordinates" };
      },

      async querySelector(data) {
        console.log("🔍 Helper: Querying selector", data.selector);
        try {
          const element = document.querySelector(data.selector);
          return {
            success: true,
            found: !!element,
            tagName: element?.tagName,
            text: element?.textContent?.slice(0, 100),
          };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },

      async getPageInfo(data) {
        console.log("📋 Helper: Getting page info");
        return {
          success: true,
          url: window.location.href,
          title: document.title,
          readyState: document.readyState,
        };
      },

      async injectScript(data) {
        console.log("💉 Helper: Injecting script");
        try {
          const result = eval(data.code);
          return { success: true, result };
        } catch (error) {
          return { success: false, error: error.message };
        }
      },
    };

    // Listen for messages
    channel.addEventListener("message", async (event) => {
      const { id, type, data } = event.data;
      console.log("📨 Helper: Received message", { id, type, data });

      try {
        let result;
        if (messageHandlers[type]) {
          result = await messageHandlers[type](data);
        } else {
          result = { success: false, error: `Unknown message type: ${type}` };
        }

        // Send response back
        channel.postMessage({
          id,
          type: "response",
          result,
        });
      } catch (error) {
        console.error("💥 Helper: Error handling message", error);
        channel.postMessage({
          id,
          type: "response",
          result: { success: false, error: error.message },
        });
      }
    });

    console.log("✅ Helper tab message handler ready");
  });

  console.log("✅ Helper tab script injected");

  // Step 3: Inject client script into main page
  await page.evaluate(() => {
    // Client script that sends messages to helper tab
    console.log("📱 Client script loaded");

    // Create BroadcastChannel for communication
    const channel = new BroadcastChannel("browser-controller");
    let messageId = 0;
    const pendingMessages = new Map();

    // Listen for responses
    channel.addEventListener("message", (event) => {
      const { id, type, result } = event.data;
      console.log("event.data", event.data);
      if (type === "response" && pendingMessages.has(id)) {
        const { resolve } = pendingMessages.get(id);
        pendingMessages.delete(id);
        resolve(result);
      }
    });

    // Helper function to send messages and wait for response
    function sendMessage(type, data, timeout = 5000) {
      return new Promise((resolve, reject) => {
        const id = ++messageId;

        // Store pending message
        pendingMessages.set(id, { resolve, reject });

        // Set timeout
        setTimeout(() => {
          if (pendingMessages.has(id)) {
            pendingMessages.delete(id);
            reject(new Error(`Message timeout: ${type}`));
          }
        }, timeout);

        // Send message
        channel.postMessage({ id, type, data });
      });
    }

    // Expose browser controller API
    window.browserController = {
      async init() {
        console.log("🚀 Navigation-safe browser controller initialized");
        return { success: true };
      },

      async click(x, y) {
        return await sendMessage("click", { x, y });
      },

      async querySelector(selector) {
        return await sendMessage("querySelector", { selector });
      },

      async getPageInfo() {
        return await sendMessage("getPageInfo", {});
      },

      async injectScript(code) {
        return await sendMessage("injectScript", { code });
      },

      async takeScreenshot() {
        // Screenshots handled by backend
        console.log(
          "📸 Screenshot requests should be handled by backend Puppeteer"
        );
        return {
          success: false,
          error: "Use backend Puppeteer for screenshots",
        };
      },
    };

    console.log("✅ Client browser controller ready");
  });

  console.log("✅ Client script injected into main page");

  // Step 4: Initialize the browser controller
  await page.evaluate(() => {
    return window.browserController.init();
  });

  console.log("✅ Navigation-safe browser controller initialized");

  // Wait for setup to stabilize
  await new Promise((resolve) => setTimeout(resolve, 2000));

  console.log(
    "3️⃣ Testing GitHub logo click with navigation-safe controller..."
  );

  try {
    // Wait for the GitHub logo to be available
    await page.waitForSelector('a[aria-label="Homepage"]', { timeout: 10000 });
    console.log("✅ GitHub logo found");

    // Add a click listener to monitor what happens
    await page.evaluate(() => {
      const logo = document.querySelector('a[aria-label="Homepage"]');
      if (logo) {
        console.log("🎯 Adding click listener to GitHub logo");
        logo.addEventListener("click", (e) => {
          console.log("🖱️ GitHub logo clicked!", e);
          console.log("🖱️ Target:", e.target);
          console.log("🖱️ Current URL:", window.location.href);
        });
      }
    });

    console.log("4️⃣ Clicking GitHub logo in 3 seconds...");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    // Click the GitHub logo using our navigation-safe controller
    // await page.click('a[aria-label="Homepage"]');
    console.log("✅ GitHub logo clicked successfully");

    // Wait to see if browser crashes
    await new Promise((resolve) => setTimeout(resolve, 5000));
    console.log("✅ Browser still alive after 5 seconds");

    // Test the browser controller functionality
    console.log("5️⃣ Testing browser controller functionality...");

    const pageInfo = await page.evaluate(() => {
      return window.browserController.getPageInfo();
    });
    console.log("📋 Page info:", pageInfo);

    const queryResult = await page.evaluate(() => {
      return window.browserController.querySelector('a[aria-label="Homepage"]');
    });
    console.log("🔍 Query result:", queryResult);
  } catch (error) {
    console.error("💥 ERROR during GitHub logo test:", error.message);
    console.error("💥 ERROR STACK:", error.stack);
  }

  console.log("🏁 Test completed with navigation-safe architecture");
})();
