// Type definitions for simple-cdp
import type { ProtocolMapping } from "devtools-protocol/types/protocol-mapping";

interface CDPOptions {
  apiUrl?: string;
  apiPath?: string;
  webSocketDebuggerUrl?: string;
  apiPathTargets?: string;
  apiPathNewTarget?: string;
  apiPathActivateTarget?: string;
  apiPathCloseTarget?: string;
  connectionMaxRetry?: number;
  connectionRetryDelay?: number;
}

type CDPValue =
  | string
  | number
  | boolean
  | CDPValue[]
  | { [key: string]: CDPValue };

interface CDPObject {
  [key: string]: CDPValue;
}

interface CDPConnection extends EventTarget {
  open(): Promise<void>;
  sendMessage(
    method: string,
    params?: CDPObject,
    sessionId?: string
  ): Promise<any>;
  close(): void;
}

interface CDPEvent {
  type: string;
  params: any;
  sessionId?: string;
}

type CDPEventListener = (event: CDPEvent) => void | Promise<void>;

interface CDPTargetInfo {
  id: string;
  type: string;
  title: string;
  url: string;
  webSocketDebuggerUrl: string;
}

interface CDPError extends Error {
  code?: string;
  status?: number;
}

// Domain event listener methods
interface CDPDomainListeners {
  addEventListener(type: string, listener: CDPEventListener): void;
  removeEventListener(type: string, listener: CDPEventListener): void;
}

// Domain method signature
type CDPDomainMethod = (
  params?: CDPObject | null,
  sessionId?: string
) => Promise<any>;

// Utility type for methods with optional parameters
type ExtractDomainMethodsWithOptionalParams<TDomain extends string> = {
  [K in keyof ProtocolMapping.Commands as K extends `${TDomain}.${infer Method}`
    ? Method
    : never]: K extends keyof ProtocolMapping.Commands
    ? ProtocolMapping.Commands[K]["paramsType"] extends []
      ? (
          params?: undefined,
          sessionId?: string
        ) => Promise<ProtocolMapping.Commands[K]["returnType"]>
      : ProtocolMapping.Commands[K]["paramsType"][0] extends undefined
      ? (
          params?: ProtocolMapping.Commands[K]["paramsType"][0],
          sessionId?: string
        ) => Promise<ProtocolMapping.Commands[K]["returnType"]>
      : (
          params: ProtocolMapping.Commands[K]["paramsType"][0],
          sessionId?: string
        ) => Promise<ProtocolMapping.Commands[K]["returnType"]>
    : never;
};

// Domain interface combining listeners and methods
interface CDPDomain extends CDPDomainListeners {
  [methodName: string]: any;
  enable: CDPDomainMethod;
  disable: CDPDomainMethod;
}

// Comprehensive typed domain interfaces using auto-generated method signatures
interface CDPPageDomain
  extends CDPDomainListeners,
    ExtractDomainMethodsWithOptionalParams<"Page"> {
  [methodName: string]: any; // Fallback for any other methods
}

interface CDPRuntimeDomain
  extends CDPDomainListeners,
    ExtractDomainMethodsWithOptionalParams<"Runtime"> {
  [methodName: string]: any;
}

interface CDPTargetDomain
  extends CDPDomainListeners,
    ExtractDomainMethodsWithOptionalParams<"Target"> {
  [methodName: string]: any;
}

interface CDPInputDomain
  extends CDPDomainListeners,
    ExtractDomainMethodsWithOptionalParams<"Input"> {
  [methodName: string]: any;
}

// Additional comprehensive domain interfaces
interface CDPNetworkDomain
  extends CDPDomainListeners,
    ExtractDomainMethodsWithOptionalParams<"Network"> {
  [methodName: string]: any;
}

interface CDPDOMDomain
  extends CDPDomainListeners,
    ExtractDomainMethodsWithOptionalParams<"DOM"> {
  [methodName: string]: any;
}

interface CDPEmulationDomain
  extends CDPDomainListeners,
    ExtractDomainMethodsWithOptionalParams<"Emulation"> {
  [methodName: string]: any;
}

interface CDPDebuggerDomain
  extends CDPDomainListeners,
    ExtractDomainMethodsWithOptionalParams<"Debugger"> {
  [methodName: string]: any;
}

// Interface for CDP instance with all domains
interface CDPInstance {
  // Core properties
  options: CDPOptions;
  connection: CDPConnection;
  reset(): void;

  // Comprehensive typed domains with all CDP methods
  Runtime: CDPRuntimeDomain;
  Target: CDPTargetDomain;
  Page: CDPPageDomain;
  Input: CDPInputDomain;
  Network: CDPNetworkDomain;
  DOM: CDPDOMDomain;
  Emulation: CDPEmulationDomain;
  Debugger: CDPDebuggerDomain;

  // Generic domains (can be extended with specific types as needed)
  Console: CDPDomain;
  CSS: CDPDomain;
  Profiler: CDPDomain;
  HeapProfiler: CDPDomain;
  Security: CDPDomain;
  ServiceWorker: CDPDomain;
  Storage: CDPDomain;
  SystemInfo: CDPDomain;
  Browser: CDPDomain;
  Animation: CDPDomain;
  Accessibility: CDPDomain;

  // Index signature for any other domains
  [domainName: string]: any;
}

const UNDEFINED_VALUE = undefined;
const MESSAGE_EVENT = "message";
const OPEN_EVENT = "open";
const CLOSE_EVENT = "close";
const ERROR_EVENT = "error";
const CONNECTION_REFUSED_ERROR_CODE = "ConnectionRefused";
const CONNECTION_ERROR_CODE = "ConnectionError";
const MIN_INVALID_HTTP_STATUS_CODE = 400;
const GET_METHOD = "GET";
const PUT_METHOD = "PUT";
const DEFAULT_URL = "http://localhost:9222";
const DEFAULT_PATH = "json/version";
const DEFAULT_PATH_TARGETS = "json";
const DEFAULT_PATH_NEW_TARGET = "json/new";
const DEFAULT_PATH_ACTIVATE_TARGET = "json/activate";
const DEFAULT_PATH_CLOSE_TARGET = "json/close";
const DEFAULT_CONNECTION_MAX_RETRY = 20;
const DEFAULT_CONNECTION_RETRY_DELAY = 500;
const DEFAULT_OPTIONS = {
  apiUrl: DEFAULT_URL,
  apiPath: DEFAULT_PATH,
  apiPathTargets: DEFAULT_PATH_TARGETS,
  apiPathNewTarget: DEFAULT_PATH_NEW_TARGET,
  apiPathActivateTarget: DEFAULT_PATH_ACTIVATE_TARGET,
  apiPathCloseTarget: DEFAULT_PATH_CLOSE_TARGET,
  connectionMaxRetry: DEFAULT_CONNECTION_MAX_RETRY,
  connectionRetryDelay: DEFAULT_CONNECTION_RETRY_DELAY,
};

class CDP implements CDPInstance {
  #connection: CDPConnection | undefined;
  #options: CDPOptions = Object.assign({}, options);
  #pendingEventListenerCalls = new Map<
    string,
    Array<{
      methodName: string;
      domainName: string;
      type: string;
      listener: CDPEventListener;
    }>
  >();

  // Domain properties with comprehensive types
  Runtime!: CDPRuntimeDomain;
  Target!: CDPTargetDomain;
  Page!: CDPPageDomain;
  Input!: CDPInputDomain;
  Network!: CDPNetworkDomain;
  DOM!: CDPDOMDomain;
  Emulation!: CDPEmulationDomain;
  Debugger!: CDPDebuggerDomain;
  Console!: CDPDomain;
  CSS!: CDPDomain;
  Profiler!: CDPDomain;
  HeapProfiler!: CDPDomain;
  Security!: CDPDomain;
  ServiceWorker!: CDPDomain;
  Storage!: CDPDomain;
  SystemInfo!: CDPDomain;
  Browser!: CDPDomain;
  Animation!: CDPDomain;
  Accessibility!: CDPDomain;
  [domainName: string]: any;

  constructor(options?: CDPOptions) {
    Object.assign(this.#options, options);

    // Initialize domain properties directly on the instance
    const domainNames = [
      "Runtime",
      "Target",
      "Page",
      "Console",
      "Network",
      "Input",
      "DOM",
      "CSS",
      "Debugger",
      "Profiler",
      "HeapProfiler",
      "Security",
      "ServiceWorker",
      "Storage",
      "SystemInfo",
      "Browser",
      "Emulation",
      "Animation",
      "Accessibility",
    ];

    domainNames.forEach((domainName) => {
      (this as any)[domainName] = this.createDomain(domainName);
    });

    // Create a proxy to handle any additional domains
    return new Proxy(this, {
      get(target: any, propertyName: string | symbol) {
        if (typeof propertyName === "string") {
          // If property exists, return it
          if (propertyName in target) {
            return target[propertyName];
          }
          // Otherwise, create a new domain
          target[propertyName] = target.createDomain(propertyName);
          return target[propertyName];
        }
        return target[propertyName];
      },
    });
  }

  createDomain(domainName: string): CDPDomain {
    const cdp = this;

    const domainProxy = new Proxy(Object.create(null), {
      get(domainTarget: any, methodName: string | symbol) {
        if (typeof methodName === "string") {
          if (methodName === "addEventListener") {
            return cdp.getDomainListenerFunction(
              "addEventListener",
              domainName
            );
          } else if (methodName === "removeEventListener") {
            return cdp.getDomainListenerFunction(
              "removeEventListener",
              domainName
            );
          } else {
            // Always create and cache the method function
            if (!domainTarget[methodName]) {
              domainTarget[methodName] = cdp.getDomainMethodFunction(
                methodName,
                domainName
              );
            }
            return domainTarget[methodName];
          }
        }
        return undefined;
      },
    });

    return domainProxy;
  }

  getDomainMethodFunction(methodName: string, domainName: string) {
    const cdp = this;
    const methodFunction = async (
      params: CDPObject | null = {},
      sessionId?: string
    ) => {
      await cdp.ready();
      const pendingEventListenerCalls =
        cdp.#pendingEventListenerCalls.get(domainName);
      if (pendingEventListenerCalls !== UNDEFINED_VALUE) {
        while (pendingEventListenerCalls.length > 0) {
          const call = pendingEventListenerCalls.shift();
          if (call && cdp.#connection) {
            (cdp.#connection as any)[call.methodName](
              `${call.domainName}.${call.type}`,
              call.listener
            );
          }
        }
        cdp.#pendingEventListenerCalls.delete(domainName);
      }
      if (!cdp.#connection) {
        throw new Error("Connection not established");
      }
      // Handle null params
      const finalParams = params || {};
      return cdp.#connection.sendMessage(
        `${domainName}.${methodName}`,
        finalParams,
        sessionId
      );
    };

    return methodFunction;
  }

  getDomainListenerFunction(methodName: string, domainName: string) {
    const cdp = this;
    return (type: string, listener: CDPEventListener) => {
      if (cdp.#connection === UNDEFINED_VALUE) {
        let pendingEventListenerCalls =
          cdp.#pendingEventListenerCalls.get(domainName);
        if (pendingEventListenerCalls === UNDEFINED_VALUE) {
          pendingEventListenerCalls = [];
          cdp.#pendingEventListenerCalls.set(
            domainName,
            pendingEventListenerCalls
          );
        }
        pendingEventListenerCalls.push({
          methodName,
          domainName,
          type,
          listener,
        });
      } else {
        (cdp.#connection as any)[methodName](`${domainName}.${type}`, listener);
      }
    };
  }

  async ready() {
    if (this.#connection === UNDEFINED_VALUE) {
      let webSocketDebuggerUrl = this.#options.webSocketDebuggerUrl;
      if (webSocketDebuggerUrl === UNDEFINED_VALUE) {
        const url = new URL(this.#options.apiPath!, this.#options.apiUrl!);
        const result = await fetchData(url, this.#options);
        webSocketDebuggerUrl = result.webSocketDebuggerUrl;
      }
      const connection = new Connection(webSocketDebuggerUrl!);
      await connection.open();
      this.#connection = connection;
    }
  }

  get options(): CDPOptions {
    return this.#options;
  }

  set options(value: CDPOptions) {
    Object.assign(this.#options, value);
  }

  get connection(): CDPConnection {
    if (!this.#connection) {
      throw new Error(
        "Connection not established. Call a CDP method first to establish connection."
      );
    }
    return this.#connection;
  }

  reset(): void {
    if (this.#connection !== UNDEFINED_VALUE) {
      this.#connection.close();
      this.#connection = UNDEFINED_VALUE;
      this.#pendingEventListenerCalls.clear();
    }
  }
  static getTargets(): Promise<CDPTargetInfo[]> {
    const { apiPathTargets, apiUrl } = options;
    return fetchData(new URL(apiPathTargets!, apiUrl!), options);
  }

  static createTarget(url?: string): Promise<CDPTargetInfo> {
    const { apiPathNewTarget, apiUrl } = options;
    const path = url ? `${apiPathNewTarget}?${url}` : apiPathNewTarget!;
    return fetchData(new URL(path, apiUrl!), options, PUT_METHOD);
  }

  static async activateTarget(targetId: string): Promise<void> {
    const { apiPathActivateTarget, apiUrl } = options;
    await fetchData(
      new URL(`${apiPathActivateTarget}/${targetId}`, apiUrl!),
      options,
      GET_METHOD,
      false
    );
  }

  static async closeTarget(targetId: string): Promise<void> {
    const { apiPathCloseTarget, apiUrl } = options;
    await fetchData(
      new URL(`${apiPathCloseTarget}/${targetId}`, apiUrl!),
      options,
      GET_METHOD,
      false
    );
  }
}

const options = Object.assign({}, DEFAULT_OPTIONS);
const cdp = new CDP(options);
const getTargets = CDP.getTargets;
const createTarget = CDP.createTarget;
const activateTarget = CDP.activateTarget;
const closeTarget = CDP.closeTarget;
export {
  cdp,
  CDP,
  options,
  getTargets,
  createTarget,
  activateTarget,
  closeTarget,
  CONNECTION_REFUSED_ERROR_CODE,
  CONNECTION_ERROR_CODE,
};

class Connection extends EventTarget implements CDPConnection {
  #webSocketDebuggerUrl: string;
  #webSocket: WebSocket | undefined;
  #pendingRequests = new Map<
    number,
    {
      resolve: (value: any) => void;
      reject: (reason: any) => void;
      method: string;
      params: CDPObject;
      sessionId?: string;
    }
  >();
  #nextRequestId = 0;

  constructor(webSocketDebuggerUrl: string) {
    super();
    this.#webSocketDebuggerUrl = webSocketDebuggerUrl;
  }

  open(): Promise<void> {
    this.#webSocket = new WebSocket(this.#webSocketDebuggerUrl);
    this.#webSocket.addEventListener(MESSAGE_EVENT, (event: MessageEvent) =>
      this.#onMessage(JSON.parse(event.data as string))
    );
    return new Promise<void>((resolve, reject) => {
      this.#webSocket!.addEventListener(OPEN_EVENT, () => resolve());
      this.#webSocket!.addEventListener(CLOSE_EVENT, (event: CloseEvent) =>
        reject(new Error(event.reason))
      );
      this.#webSocket!.addEventListener(ERROR_EVENT, () => reject(new Error()));
    });
  }
  sendMessage(
    method: string,
    params: CDPObject = {},
    sessionId?: string
  ): Promise<any> {
    if (!this.#webSocket) {
      throw new Error("WebSocket not connected");
    }

    const id = this.#nextRequestId;
    const message = JSON.stringify({ id, method, params, sessionId });
    this.#nextRequestId = (this.#nextRequestId + 1) % Number.MAX_SAFE_INTEGER;
    this.#webSocket.send(message);

    let pendingRequest: any;
    const promise = new Promise(
      (resolve, reject) =>
        (pendingRequest = { resolve, reject, method, params, sessionId })
    );
    this.#pendingRequests.set(id, pendingRequest);
    return promise;
  }

  close(): void {
    if (this.#webSocket) {
      this.#webSocket.close();
    }
  }
  #onMessage({
    id,
    method,
    result,
    error,
    params,
    sessionId,
  }: {
    id?: number;
    method?: string;
    result?: any;
    error?: { message: string; code: string };
    params?: any;
    sessionId?: string;
  }) {
    if (id !== UNDEFINED_VALUE) {
      const pendingRequest = this.#pendingRequests.get(id!);
      if (pendingRequest) {
        const { resolve, reject } = pendingRequest;
        if (error === UNDEFINED_VALUE) {
          resolve(result);
        } else {
          const message =
            error.message +
            " when calling " +
            `${pendingRequest.method}(${JSON.stringify(
              pendingRequest.params
            )})` +
            `${
              pendingRequest.sessionId === UNDEFINED_VALUE
                ? ""
                : ` (sessionId ${JSON.stringify(pendingRequest.sessionId)})`
            }`;
          const errorEvent = new Error(message) as CDPError;
          errorEvent.code = error.code;
          reject(errorEvent);
        }
        this.#pendingRequests.delete(id!);
      }
    }
    if (method !== UNDEFINED_VALUE) {
      const event = new Event(method!) as any;
      event.params = params;
      event.sessionId = sessionId;
      this.dispatchEvent(event);
    }
  }
}

function fetchData(
  url: URL,
  options: CDPOptions,
  method = GET_METHOD,
  parseJSON = true
): Promise<any> {
  return retryConnection(async () => {
    let response: Response;
    try {
      response = await fetch(url, { method });
    } catch (error) {
      const cdpError = error as CDPError;
      cdpError.code = CONNECTION_REFUSED_ERROR_CODE;
      throw cdpError;
    }
    if (response.status >= MIN_INVALID_HTTP_STATUS_CODE) {
      const error = new Error(
        response.statusText || `HTTP Error ${response.status}`
      ) as CDPError;
      error.status = response.status;
      error.code = CONNECTION_ERROR_CODE;
      throw error;
    } else {
      if (parseJSON) {
        return response.json();
      } else {
        return response.text();
      }
    }
  }, options);
}

async function retryConnection(
  fn: () => Promise<any>,
  options: CDPOptions,
  retryCount = 0
): Promise<any> {
  const { connectionMaxRetry, connectionRetryDelay } = options;
  try {
    return await fn();
  } catch (error) {
    const cdpError = error as CDPError;
    if (
      cdpError.code === CONNECTION_REFUSED_ERROR_CODE &&
      retryCount < connectionMaxRetry!
    ) {
      await new Promise((resolve) => setTimeout(resolve, connectionRetryDelay));
      return retryConnection(fn, options, retryCount + 1);
    } else {
      throw error;
    }
  }
}
