// Import WebSocket (native in modern Node.js or use 'ws' for older versions or browser-like API)
import WebSocket from "ws";

// Replace this with your WebSocket URL
const WS_URL =
  "ws://localhost:9222/devtools/browser/6005931e-1734-4e58-936b-d4dbab256e1e"; // public echo server

// Create a new WebSocket connection
const ws = new WebSocket(WS_URL);

// Event: connection opened
ws.on("open", () => {
  console.log("✅ Connected to WebSocket server");

  // Wait a second before closing
  setTimeout(() => {
    console.log("🔒 Closing WebSocket connection");
    ws.close();
  }, 1000);
});

// Event: connection closed
ws.on("close", () => {
  console.log("❌ WebSocket connection closed");
});

// Event: error
ws.on("error", (error) => {
  console.error("❗ WebSocket error:", error);
});
