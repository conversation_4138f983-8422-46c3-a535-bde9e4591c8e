(()=>{var h="message",T="open",f="close",y="error",p="ConnectionRefused",w="ConnectionError";var C="GET",L="PUT",v="http://localhost:9222",N="json/version",O="json",b="json/new",U="json/activate",_="json/close";var A={apiUrl:v,apiPath:N,apiPathTargets:O,apiPathNewTarget:b,apiPathActivateTarget:U,apiPathCloseTarget:_,connectionMaxRetry:20,connectionRetryDelay:500},g=class{#t;#e=Object.assign({},D);#n=new Map;Runtime;Target;Page;Input;Network;DOM;Emulation;Debugger;Console;CSS;Profiler;HeapProfiler;Security;ServiceWorker;Storage;SystemInfo;Browser;Animation;Accessibility;constructor(e){return Object.assign(this.#e,e),["Runtime","Target","<PERSON>","Console","Network","Input","DOM","CSS","Debugger","Profiler","HeapProfiler","Security","ServiceWorker","Storage","SystemInfo","Browser","Emulation","Animation","Accessibility"].forEach(t=>{this[t]=this.createDomain(t)}),new Proxy(this,{get(t,i){return typeof i=="string"&&(i in t||(t[i]=t.createDomain(i))),t[i]}})}createDomain(e){let n=this;return new Proxy(Object.create(null),{get(i,s){if(typeof s=="string")return s==="addEventListener"?n.getDomainListenerFunction("addEventListener",e):s==="removeEventListener"?n.getDomainListenerFunction("removeEventListener",e):(i[s]||(i[s]=n.getDomainMethodFunction(s,e)),i[s])}})}getDomainMethodFunction(e,n){let t=this;return async(s={},r)=>{await t.ready();let o=t.#n.get(n);if(o!==void 0){for(;o.length>0;){let a=o.shift();a&&t.#t&&t.#t[a.methodName](`${a.domainName}.${a.type}`,a.listener)}t.#n.delete(n)}if(!t.#t)throw new Error("Connection not established");let c=s||{};return t.#t.sendMessage(`${n}.${e}`,c,r)}}getDomainListenerFunction(e,n){let t=this;return(i,s)=>{if(t.#t===void 0){let r=t.#n.get(n);r===void 0&&(r=[],t.#n.set(n,r)),r.push({methodName:e,domainName:n,type:i,listener:s})}else t.#t[e](`${n}.${i}`,s)}}async ready(){if(this.#t===void 0){let e=this.#e.webSocketDebuggerUrl;if(e===void 0){let t=new URL(this.#e.apiPath,this.#e.apiUrl);e=(await l(t,this.#e)).webSocketDebuggerUrl}let n=new d(e);await n.open(),this.#t=n}}get options(){return this.#e}set options(e){Object.assign(this.#e,e)}get connection(){if(!this.#t)throw new Error("Connection not established. Call a CDP method first to establish connection.");return this.#t}reset(){this.#t!==void 0&&(this.#t.close(),this.#t=void 0,this.#n.clear())}static getTargets(){let{apiPathTargets:e,apiUrl:n}=D;return l(new URL(e,n),D)}static createTarget(e){let{apiPathNewTarget:n,apiUrl:t}=D,i=e?`${n}?${e}`:n;return l(new URL(i,t),D,L)}static async activateTarget(e){let{apiPathActivateTarget:n,apiUrl:t}=D;await l(new URL(`${n}/${e}`,t),D,C,!1)}static async closeTarget(e){let{apiPathCloseTarget:n,apiUrl:t}=D;await l(new URL(`${n}/${e}`,t),D,C,!1)}},D=Object.assign({},A),I=new g(D),M=g.getTargets,R=g.createTarget,S=g.activateTarget,x=g.closeTarget;var d=class extends EventTarget{#t;#e;#n=new Map;#i=0;constructor(e){super(),this.#t=e}open(){return this.#e=new WebSocket(this.#t),this.#e.addEventListener(h,e=>this.#s(JSON.parse(e.data))),new Promise((e,n)=>{this.#e.addEventListener(T,()=>e()),this.#e.addEventListener(f,t=>n(new Error(t.reason))),this.#e.addEventListener(y,()=>n(new Error))})}sendMessage(e,n={},t){if(!this.#e)throw new Error("WebSocket not connected");let i=this.#i,s=JSON.stringify({id:i,method:e,params:n,sessionId:t});this.#i=(this.#i+1)%Number.MAX_SAFE_INTEGER,this.#e.send(s);let r,o=new Promise((c,a)=>r={resolve:c,reject:a,method:e,params:n,sessionId:t});return this.#n.set(i,r),o}close(){this.#e&&this.#e.close()}#s({id:e,method:n,result:t,error:i,params:s,sessionId:r}){if(e!==void 0){let o=this.#n.get(e);if(o){let{resolve:c,reject:a}=o;if(i===void 0)c(t);else{let P=i.message+` when calling ${o.method}(${JSON.stringify(o.params)})${o.sessionId===void 0?"":` (sessionId ${JSON.stringify(o.sessionId)})`}`,E=new Error(P);E.code=i.code,a(E)}this.#n.delete(e)}}if(n!==void 0){let o=new Event(n);o.params=s,o.sessionId=r,this.dispatchEvent(o)}}};function l(m,e,n=C,t=!0){return u(async()=>{let i;try{i=await fetch(m,{method:n})}catch(s){let r=s;throw r.code=p,r}if(i.status>=400){let s=new Error(i.statusText||`HTTP Error ${i.status}`);throw s.status=i.status,s.code=w,s}else return t?i.json():i.text()},e)}async function u(m,e,n=0){let{connectionMaxRetry:t,connectionRetryDelay:i}=e;try{return await m()}catch(s){if(s.code===p&&n<t)return await new Promise(o=>setTimeout(o,i)),u(m,e,n+1);throw s}}(function(){let m={debug:!0},e=null,n,t=!1;function i(...c){m.debug&&console.log("[browserController]",...c)}function s(...c){console.error("[browserController]",...c)}async function r(c,a){i("Connecting to CDP and attaching to target:",a),await o(c,a),i("Browser controller initialized with sessionId:",n)}async function o(c,a){try{e=new g({webSocketDebuggerUrl:c}),i("Attaching to target:",a,c);let{sessionId:P}=await e.Target.attachToTarget({targetId:a,flatten:!0});await e.Page.enable(void 0,P),await e.Runtime.enable(void 0,P),i("\u2713 Browser controller attached to target",a,"with sessionId:",P)}catch(P){throw s("Failed to connect to CDP and attach to target:",P),P}}globalThis.browserController={init:r}})();})();
